#!/usr/bin/env python3
"""
调试API调用问题
"""

import asyncio
import json
from openai import AsyncOpenAI
from schemas import RetrieveNERExamplesTool, ExtractionTool
from model_interface import pydantic_to_openai_tool
from config import CONFIG

async def debug_api_call():
    """直接调试API调用"""
    
    print("🔍 调试API调用...")
    
    # 配置
    api_config = CONFIG.get('api_config', {})
    base_url = api_config.get('base_url', 'http://8.138.94.162:3001/proxy/silicon/v1')
    model_name = api_config.get('model_name', 'Qwen/Qwen2.5-7B-Instruct')
    api_key = api_config.get('api_key', 'sk-zhongyushi')
    
    print(f"📋 配置信息:")
    print(f"  Base URL: {base_url}")
    print(f"  Model: {model_name}")
    print(f"  API Key: {api_key[:10]}...")
    
    # 构建工具Schema
    tools = [RetrieveNERExamplesTool, ExtractionTool]
    tool_schemas = [pydantic_to_openai_tool(tool) for tool in tools]
    
    print(f"\n🔧 工具数量: {len(tool_schemas)}")
    
    # 构建消息
    test_text = "Apple Inc. is a technology company."
    prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from the text: "{test_text}"

Use these entity types: person, organization, location

You MUST call exactly ONE tool."""

    messages = [{"role": "user", "content": prompt}]
    
    print(f"\n📝 Prompt: {prompt}")
    
    try:
        # 创建客户端
        client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=60
        )
        
        print(f"\n🚀 发送API请求...")
        
        # 发送请求
        response = await client.chat.completions.create(
            model=model_name,
            messages=messages,
            tools=tool_schemas,
            tool_choice="auto",
            temperature=0.1
        )
        
        await client.close()
        
        print(f"\n✅ API响应成功:")
        print(f"  响应类型: {type(response)}")
        print(f"  消息: {response.choices[0].message}")
        
        message = response.choices[0].message
        if hasattr(message, 'tool_calls') and message.tool_calls:
            print(f"\n🔧 工具调用:")
            for i, tool_call in enumerate(message.tool_calls):
                print(f"  工具 {i+1}: {tool_call.function.name}")
                print(f"  参数: {tool_call.function.arguments}")
        else:
            print(f"\n❌ 无工具调用")
            if hasattr(message, 'content'):
                print(f"  内容: {message.content}")
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_api_call())
