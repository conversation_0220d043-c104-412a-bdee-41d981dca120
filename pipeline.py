#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 Pipeline模块 - 元认知智能体架构
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import logging
from typing import Dict, List

# 🧠 新架构：使用元认知智能体
from meta_cognitive_agent import get_meta_cognitive_agent

logger = logging.getLogger(__name__)

# --- 🧠 元认知智能体NER流程 ---

async def run_meta_cognitive_ner_pipeline(query: str, pre_initialized_retriever=None) -> Dict[str, List[str]]:
    """
    🧠 元认知智能体NER流水线

    核心创新：
    1. 单一超级Prompt：LLM一次性完成思考、决策和执行
    2. Function Calling驱动：LLM自主决策是否需要检索示例
    3. 多路并行检索：语义优先 + 标签辅助 + 智能融合
    4. 三级火箭检索：硬性过滤 → 语义召回 → 重排器精排
    """
    try:
        logger.info(f"🧠 启动元认知智能体NER: '{query[:50]}...'")

        # 🎯 核心：使用元认知智能体
        agent = get_meta_cognitive_agent(pre_initialized_retriever)

        # 🚀 一次调用完成所有任务
        entities = await agent.extract_entities(query)

        # 显示结果
        if entities:
            total_entities = sum(len(v) for v in entities.values() if isinstance(v, list))

            if total_entities > 0:
                logger.info(f"✅ 元认知智能体NER完成: 识别 {total_entities} 个实体")

                print("\n" + "="*60)
                print("🧠 元认知智能体NER结果")
                print("="*60)
                print(f"✅ 成功识别 {total_entities} 个实体")
                for entity_type, entity_list in entities.items():
                    if entity_list:  # 只显示非空的实体类型
                        print(f"🏷️ {entity_type}: {entity_list}")
                print("="*60)
            else:
                logger.warning("⚠️ 未识别到任何实体")
        else:
            logger.warning("⚠️ 未识别到任何实体")

        # 🔧 清理结果：移除空数组，只返回有实体的类型
        if entities:
            cleaned_entities = {k: v for k, v in entities.items() if isinstance(v, list) and v}
            return cleaned_entities
        else:
            return {}

    except Exception as e:
        logger.error(f"❌ 元认知智能体NER失败: {e}")
        return {}


# 🎯 重构完成 - 真正的元认知智能体架构
# 使用方式：
# from pipeline import run_meta_cognitive_ner_pipeline
# result = await run_meta_cognitive_ner_pipeline("your query here", pre_initialized_retriever)
