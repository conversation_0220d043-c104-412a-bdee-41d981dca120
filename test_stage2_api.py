#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 测试Stage 2的API调用
"""

import asyncio
from model_interface import model_service

async def test_simple_api():
    print('🔍 测试Stage 2的API调用...')
    
    # 模拟Stage 2的调用
    messages = [{
        'role': 'user', 
        'content': '''You are an expert Named Entity Recognition system.

Here are some examples to help you:

Example 1:
Text: "<PERSON> works at Apple Inc. in New York."
Entities: {"person": ["<PERSON>"], "organization": ["Apple Inc."], "location": ["New York"]}

Now extract named entities from the following text using these entity types: person, organization, location

Text to analyze: "NOVAK Welcome back."

Return the entities in JSON format like: {"person": ["<PERSON>"], "organization": ["Apple Inc."], "location": ["New York"]}'''
    }]
    
    try:
        print('🚀 发送Stage 2 API请求...')
        response = await model_service.generate_simple_async(messages, temperature=0.1)
        print(f'✅ API响应成功: {len(response)} 字符')
        print(f'📝 响应内容: {response[:200]}...')
        
        # 测试JSON解析
        import re
        import json
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            entities_json = json_match.group()
            entities = json.loads(entities_json)
            print(f'✅ JSON解析成功: {entities}')
        else:
            print('❌ 未找到JSON格式')
            
    except Exception as e:
        print(f'❌ API调用失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_api())
