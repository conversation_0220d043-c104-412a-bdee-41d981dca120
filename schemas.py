from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional

# ====== 核心NER数据模型 ======
# 移除未使用的Entity类，保持简洁


class ExtractionTool(BaseModel):
    """The tool that the LLM is expected to call with the extracted entities."""
    entities: Dict[str, List[str]] = Field(
        default_factory=dict,
        description="Entities grouped by type. Use the exact entity type names specified in the prompt. Return {} if no entities found."
    )


class RetrievalOrderTool(BaseModel):
    """Tool for generating retrieval orders in meta-cognitive analysis."""
    description: str = Field(
        description="Natural language description of needed teaching examples"
    )
    required_features: Dict[str, List[str]] = Field(
        description="Required fact tags including entity_types and structural_tags"
    )


# ====== 元认知智能体数据模型 ======

class DifficultyAnalysis(BaseModel):
    """🧠 Meta-Cognitive Planner: Stage 1 - Difficulty identification result"""
    difficulties: List[str] = Field(
        description="List of identified NER difficulties (e.g., 'abbreviations', 'nested entities', 'domain-specific terms')"
    )
    confidence: float = Field(
        description="Confidence score for the difficulty analysis (0.0-1.0)",
        ge=0.0, le=1.0
    )
    reasoning: str = Field(
        description="Detailed reasoning process for difficulty identification"
    )


class TeachingRequirement(BaseModel):
    """🧠 Meta-Cognitive Planner: Stage 2 - Teaching requirement description"""
    description: str = Field(
        description="Natural language description of what kind of examples are needed"
    )
    target_difficulties: List[str] = Field(
        description="Specific difficulties this requirement aims to address"
    )
    context_info: Dict[str, Any] = Field(
        description="Additional context information about the query",
        default_factory=dict
    )
    priority: int = Field(
        description="Priority level for this requirement (1=highest, 5=lowest)",
        default=1, ge=1, le=5
    )


class TeachingOrder(BaseModel):
    """🧠 Meta-Cognitive Planner: Stage 3 - Structured teaching demand order (JSON protocol)"""
    must_contain_entities: List[str] = Field(
        description="Entity types that examples MUST contain",
        default_factory=list
    )
    structural_features: List[str] = Field(
        description="Required structural features (e.g., 'short_text', 'complex_sentence', 'multiple_entities')",
        default_factory=list
    )
    difficulty_focus: List[str] = Field(
        description="Specific difficulties to focus on",
        default_factory=list
    )
    description: str = Field(
        description="Semantic description for vector retrieval"
    )
    domain_preference: Optional[str] = Field(
        description="Preferred domain (e.g., 'financial', 'academic', 'news')",
        default=None
    )
    priority: int = Field(
        description="Order priority (1=highest)",
        default=1, ge=1, le=5
    )


class ScoredExample(BaseModel):
    """🔍 Demand-Driven Executor: Example with relevance score"""
    example: Dict[str, Any] = Field(
        description="Original example data (text, label, metadata)"
    )
    relevance_score: float = Field(
        description="Relevance score from reranker (0.0-1.0)",
        ge=0.0, le=1.0
    )
    source: str = Field(
        description="Source identifier (e.g., 'vector_retrieval', 'reranker', 'fallback')"
    )
    metadata: Dict[str, Any] = Field(
        description="Additional metadata about the example",
        default_factory=dict
    )


# ====== 系统运行时数据模型 ======

class QueryFeatures(BaseModel):
    """📊 Query feature extraction for parameter prediction"""
    text_length: int = Field(description="Length of input text in characters")
    word_count: int = Field(description="Number of words in the text")
    potential_entity_count: int = Field(description="Estimated number of potential entities")
    unique_word_ratio: float = Field(description="Ratio of unique words to total words")
    domain_indicators: List[str] = Field(description="Domain indicator keywords", default_factory=list)
    complexity_score: float = Field(description="Text complexity score (0.0-1.0)", default=0.5)


class RetrievalParams(BaseModel):
    """🎛️ Dynamic retrieval parameters"""
    cosine_top_k: int = Field(description="Top-K for cosine similarity retrieval", default=50)
    reranker_top_k: int = Field(description="Top-K for reranker refinement", default=20)
    final_count: int = Field(description="Final number of examples to use", default=5)
    semantic_weight: float = Field(description="Weight for semantic similarity", default=0.6)
    syntax_weight: float = Field(description="Weight for syntactic similarity", default=0.3)
    diversity_weight: float = Field(description="Weight for diversity", default=0.1)
    score_threshold: float = Field(description="Minimum score threshold for quality", default=0.7)


class TrainingRecord(BaseModel):
    """📈 Training record for parameter predictor"""
    features: QueryFeatures = Field(description="Query features")
    params_used: RetrievalParams = Field(description="Parameters that were used")
    f1_score: float = Field(description="Achieved F1 score")
    timestamp: str = Field(description="Timestamp of the record")
    query_text: str = Field(description="Original query text for reference")


# ====== LangChain编排数据模型 ======

class RetrievalCriteria(BaseModel):
    """🎯 LLM驱动的动态检索指标"""
    target_entity_types: List[str] = Field(
        description="Target entity types to focus on (e.g., ['person', 'organization'])",
        default_factory=list
    )
    text_complexity: str = Field(
        description="Preferred text complexity level: 'simple', 'medium', 'complex'",
        default="medium"
    )
    domain_focus: str = Field(
        description="Domain focus for examples (e.g., 'business', 'technology', 'general')",
        default="general"
    )
    structural_preferences: List[str] = Field(
        description="Structural preferences (e.g., ['short_sentences', 'multiple_entities'])",
        default_factory=list
    )
    difficulty_level: str = Field(
        description="Difficulty level: 'easy', 'medium', 'hard'",
        default="medium"
    )
    reasoning: str = Field(
        description="LLM's reasoning for these criteria"
    )


class AgentState(BaseModel):
    """🤖 Agent state for workflow orchestration"""
    query_text: str = Field(description="Original input text")
    difficulties: Optional[DifficultyAnalysis] = Field(description="Identified difficulties", default=None)
    requirements: Optional[TeachingRequirement] = Field(description="Teaching requirements", default=None)
    order: Optional[TeachingOrder] = Field(description="Structured teaching order", default=None)
    scored_examples: List[ScoredExample] = Field(description="Retrieved and scored examples", default_factory=list)
    final_examples: List[Dict[str, Any]] = Field(description="Final selected examples", default_factory=list)
    stage: str = Field(description="Current processing stage", default="init")
    selection_method: Optional[str] = Field(description="Method used for example selection", default=None)
    error_message: Optional[str] = Field(description="Error message if any", default=None)
    metadata: Dict[str, Any] = Field(description="Additional state metadata", default_factory=dict)
    retrieval_criteria: Optional[RetrievalCriteria] = Field(description="LLM-generated retrieval criteria", default=None)


# ====== 🚀 元认知智能体Function Calling工具 ======

# {{ AURA-X: Remove - 简化Schema，移除RetrievalRequest类. Approval: 寸止(ID:1738230400). }}
# 不再需要复杂的RetrievalRequest，直接使用description和k


class RetrieveNERExamplesTool(BaseModel):
    """🧠 检索NER示例工具 - LLM通过此工具获取相关教学示例"""
    description: str = Field(
        description="详细描述需要什么样的NER示例来帮助解决当前文本的实体识别难点。请具体说明遇到的问题和期望的示例类型。"
    )
    k: int = Field(
        description="需要检索的示例数量，建议1-5个",
        default=3,
        ge=1,
        le=10
    )

