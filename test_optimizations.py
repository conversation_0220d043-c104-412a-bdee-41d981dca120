#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 测试所有优化效果
验证FAISS多线程、BatchProcessor聚合、连接池等优化
"""

import asyncio
import time
import logging
from example_retriever import example_retriever

# 设置日志级别
logging.basicConfig(level=logging.INFO)

async def test_single_retrieval():
    """测试单次检索性能"""
    print('\n🎯 测试单次检索性能...')
    
    query = "person names and individuals in business context"
    
    # 预热
    await example_retriever.simple_retrieve(query, k=3)
    
    # 测试多次单次检索
    times = []
    for i in range(5):
        start_time = time.time()
        result = await example_retriever.simple_retrieve(query, k=3)
        end_time = time.time()
        
        if result:
            times.append(end_time - start_time)
            print(f'  第{i+1}次: {end_time - start_time:.3f}秒, 返回{len(result)}个结果')
    
    if times:
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        print(f'\n📊 单次检索统计:')
        print(f'  平均耗时: {avg_time:.3f}秒')
        print(f'  最快耗时: {min_time:.3f}秒')
        print(f'  最慢耗时: {max_time:.3f}秒')

async def test_concurrent_retrieval():
    """测试并发检索性能"""
    print('\n🚀 测试并发检索性能...')
    
    # 测试查询
    test_queries = [
        'person names and individuals in business context',
        'organization and company names in technology sector', 
        'location and place names in news articles',
        'financial entities and monetary amounts',
        'technology companies and their products',
        'government organizations and agencies',
        'geographic locations and addresses',
        'people names in professional context'
    ]
    
    print(f'📝 测试查询数量: {len(test_queries)}')
    
    # 测试不同并发级别
    for concurrency in [1, 5, 10, 20]:
        print(f'\n📊 测试并发数: {concurrency}')
        
        # 创建并发任务
        tasks = []
        for i in range(concurrency):
            query = test_queries[i % len(test_queries)]
            task = example_retriever.simple_retrieve(query, k=3)
            tasks.append(task)
        
        # 测量执行时间
        start_time = time.time()
        results = await asyncio.gather(*tasks)
        end_time = time.time()
        
        # 统计结果
        total_time = end_time - start_time
        successful_requests = sum(1 for r in results if r)
        avg_time = total_time / concurrency if concurrency > 0 else 0
        
        print(f'  ⏱️ 总耗时: {total_time:.2f}秒')
        print(f'  📈 平均耗时: {avg_time:.2f}秒/请求')
        print(f'  ✅ 成功率: {successful_requests}/{concurrency} ({successful_requests/concurrency*100:.1f}%)')
        print(f'  🔍 QPS: {concurrency/total_time:.1f} 请求/秒')

async def test_batch_processor():
    """测试BatchProcessor聚合效果"""
    print('\n🔧 测试BatchProcessor聚合效果...')
    
    # 获取BatchProcessor配置
    batch_processor = example_retriever.batch_processor
    print(f'  批处理大小: {batch_processor.batch_size}')
    print(f'  等待时间: {batch_processor.timeout}秒')
    
    # 模拟高并发请求
    print('\n🚀 模拟高并发请求...')
    
    queries = [
        'person entities in text',
        'organization names',
        'location references',
        'company mentions',
        'people names'
    ]
    
    # 创建大量并发请求
    tasks = []
    for i in range(15):  # 15个并发请求，应该能触发批处理
        query = queries[i % len(queries)]
        task = example_retriever.simple_retrieve(query, k=3)
        tasks.append(task)
    
    start_time = time.time()
    results = await asyncio.gather(*tasks)
    end_time = time.time()
    
    successful = sum(1 for r in results if r)
    total_time = end_time - start_time
    
    print(f'  📊 15个并发请求结果:')
    print(f'    总耗时: {total_time:.2f}秒')
    print(f'    成功率: {successful}/15 ({successful/15*100:.1f}%)')
    print(f'    平均耗时: {total_time/15:.2f}秒/请求')

async def main():
    """主测试函数"""
    try:
        print('🚀 开始优化效果测试...')
        
        # 初始化向量库
        print('📚 初始化向量库...')
        await example_retriever.initialize_vector_store()
        print('✅ 向量库初始化完成')
        
        # 运行各项测试
        await test_single_retrieval()
        await test_concurrent_retrieval()
        await test_batch_processor()
        
        print('\n🎉 所有优化测试完成！')
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
