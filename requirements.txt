# 🧠 APIICL - 元认知智能体NER系统依赖包

# ====== 核心框架 ======
# LangChain框架 - 用于工作流编排和Memory管理
langchain>=0.1.0
langchain-core>=0.1.0
langchain-community>=0.0.20

# 数据验证和模型
pydantic>=2.0.0

# 异步HTTP客户端
aiohttp>=3.8.0
httpx>=0.24.0

# ====== 数据处理 ======
# 数值计算
numpy>=1.21.0

# 进度条显示
tqdm>=4.64.0

# JSON处理（Python内置，但列出以防版本问题）
# json - 内置模块

# ====== 可选依赖 ======
# 如果需要向量存储功能
# faiss-cpu>=1.7.0  # Facebook AI Similarity Search
# chromadb>=0.4.0   # Chroma向量数据库

# 如果需要更多LLM模型支持
# openai>=1.0.0     # OpenAI API
# anthropic>=0.3.0  # Anthropic Claude API

# 如果需要本地嵌入模型
# sentence-transformers>=2.2.0
# transformers>=4.21.0
# torch>=1.12.0

# ====== 开发和测试 ======
# 测试框架（可选）
# pytest>=7.0.0
# pytest-asyncio>=0.21.0

# 代码格式化（可选）
# black>=22.0.0
# isort>=5.10.0
