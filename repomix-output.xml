This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: example_retriever.py, meta_cognitive_agent.py, model_interface.py, pipeline.py, main.py, config.py
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
config.py
example_retriever.py
main.py
meta_cognitive_agent.py
model_interface.py
pipeline.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="example_retriever.py">
"""
示例检索器 - 简单高效的NER示例检索系统
遵循KISS原则：保持简单，避免过度设计
"""

import asyncio
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import json
import os
import time
import pickle
import hashlib
import threading
from collections import defaultdict

try:
    import faiss
    FAISS_AVAILABLE = True
    # {{ AURA-X: Add - 启用FAISS多线程优化，提升检索性能. Approval: 寸止(ID:1738230400). }}
    # 设置FAISS多线程数量为CPU核心数的一半，避免过度竞争
    import os
    cpu_count = os.cpu_count() or 4
    faiss_threads = max(1, cpu_count // 2)
    try:
        faiss.omp_set_num_threads(faiss_threads)
        logging.info(f"✅ FAISS多线程已启用: {faiss_threads} 线程")
    except AttributeError:
        # 某些FAISS版本可能没有omp_set_num_threads
        logging.info("✅ FAISS已加载，使用默认线程配置")
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available, falling back to simple vector search")

from schemas import RetrievalCriteria
from model_interface import model_service
from config import CONFIG, get_current_dataset_path

logger = logging.getLogger(__name__)


class BatchProcessor:
    """异步批处理器 - 收集并发请求进行批量处理"""

    def __init__(self, batch_size: int = 50, timeout: float = 2.0):
        # {{ AURA-X: Modify - 优化批处理参数，提升高并发性能. Approval: 寸止(ID:1738230400). }}
        self.batch_size = batch_size  # 增加到50，充分利用API批处理能力
        self.timeout = timeout  # 增加到2秒，让更多并发请求聚集
        self.pending_requests = []
        self.request_futures = {}
        self.lock = asyncio.Lock()
        self.processing = False

    async def add_request(self, request_id: str, description: str) -> List[float]:
        """添加嵌入请求到批处理队列"""
        future = asyncio.Future()

        async with self.lock:
            self.pending_requests.append((request_id, description))
            self.request_futures[request_id] = future

            # 如果达到批处理大小或者是第一个请求，触发处理
            if len(self.pending_requests) >= self.batch_size or not self.processing:
                asyncio.create_task(self._process_batch())

        return await future

    async def _process_batch(self):
        """处理当前批次的请求"""
        if self.processing:
            return

        self.processing = True

        try:
            # 等待一小段时间收集更多请求
            await asyncio.sleep(self.timeout)

            async with self.lock:
                if not self.pending_requests:
                    return

                # 获取当前批次
                current_batch = self.pending_requests.copy()
                self.pending_requests.clear()

            # 批量生成嵌入
            descriptions = [desc for _, desc in current_batch]
            request_ids = [req_id for req_id, _ in current_batch]

            try:
                embeddings = await model_service.get_embeddings_async(descriptions)

                # 分发结果
                for i, request_id in enumerate(request_ids):
                    if request_id in self.request_futures:
                        future = self.request_futures.pop(request_id)
                        if i < len(embeddings):
                            future.set_result(embeddings[i])
                        else:
                            future.set_result([])

            except Exception as batch_error:
                # 处理失败，返回空结果
                for request_id in request_ids:
                    if request_id in self.request_futures:
                        future = self.request_futures.pop(request_id)
                        future.set_result([])
                logger.error(f"批处理失败: {batch_error}")

        finally:
            self.processing = False




class FAISSVectorStore:
    """FAISS高性能向量存储"""

    def __init__(self):
        self.examples = []
        self.metadata = []
        self.embeddings = []
        self.index = None
        self.dimension = None
        self.initialized = False

    def add_examples(self, examples: List[Dict], embeddings: List[List[float]], metadata: List[Dict]):
        """添加示例和向量"""
        self.examples.extend(examples)
        self.metadata.extend(metadata)
        self.embeddings.extend(embeddings)

        if not self.dimension and embeddings:
            self.dimension = len(embeddings[0])

        self._build_index()

    def _build_index(self):
        """构建FAISS索引"""
        if not self.embeddings or not FAISS_AVAILABLE:
            return

        try:
            # 转换为numpy数组
            embeddings_array = np.array(self.embeddings, dtype=np.float32)

            # 创建FAISS索引
            if self.dimension:
                # {{ AURA-X: Modify - 优化FAISS索引配置，提升多线程性能. Approval: 寸止(ID:1738230400). }}
                # 对于小数据集，直接使用简单索引
                if len(self.embeddings) < 5000:
                    self.index = faiss.IndexFlatIP(self.dimension)
                    # IndexFlatIP不需要设置nprobe，它是精确搜索
                else:
                    # 大数据集使用IVF索引，优化多线程性能
                    nlist = min(100, len(self.embeddings) // 40)  # 确保每个聚类有足够的点
                    quantizer = faiss.IndexFlatIP(self.dimension)
                    self.index = faiss.IndexIVFFlat(quantizer, self.dimension, nlist)
                    self.index.train(embeddings_array)
                    # 设置搜索参数
                    self.index.nprobe = min(10, nlist)  # 平衡精度和速度

                self.index.add(embeddings_array)
                self.initialized = True
                logger.info(f"✅ FAISS索引构建完成: {len(self.embeddings)}个向量, 维度{self.dimension}, 多线程已启用")

        except Exception as e:
            logger.error(f"FAISS索引构建失败: {e}")
            self.initialized = False

    def search(self, query_embedding: List[float], top_k: int = 20) -> List[Tuple[int, float]]:
        """FAISS向量检索"""
        if not self.initialized or not self.index:
            return []

        try:
            query_array = np.array([query_embedding], dtype=np.float32)
            scores, indices = self.index.search(query_array, min(top_k, len(self.embeddings)))

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0:  # FAISS返回-1表示无效结果
                    results.append((int(idx), float(score)))

            return results

        except Exception as e:
            logger.error(f"FAISS检索失败: {e}")
            return []


class ExampleRetriever:
    """高性能NER任务示例检索器 - 支持FAISS+批处理"""

    def __init__(self):
        self.config = CONFIG.get('retrieval_config', {})
        self.model_service = model_service

        # 选择向量存储后端
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS is not available, which is required for the vector store. Please install it using 'pip install faiss-cpu' or 'pip install faiss-gpu'.")
        self.vector_store = FAISSVectorStore()

        self.batch_processor = BatchProcessor(batch_size=10, timeout=0.1)
        self.initialized = False
        
        logger.info("示例检索器初始化完成")




    async def _load_pkl_cache(self, pkl_file: str, data_path: str) -> bool:
        """加载pkl缓存"""
        try:
            with open(pkl_file, 'rb') as f:
                cached_data = pickle.load(f)

            # 验证缓存
            if not self._validate_cache(cached_data, data_path):
                logger.warning("缓存验证失败，重新生成...")
                return False

            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data.get('metadata', [{}] * len(examples))
            self.vector_store.add_examples(examples, embeddings, metadata)

            logger.info(f"从pkl缓存加载 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"加载pkl缓存失败: {e}")
            return False

    async def _migrate_json_to_pkl(self, json_file: str, pkl_file: str, data_path: str) -> bool:
        """将JSON缓存迁移到pkl格式"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 添加版本信息
            pkl_data = {
                'examples': json_data['examples'],
                'embeddings': json_data['embeddings'],
                'dataset_path': data_path,
                'created_at': json_data.get('created_at', time.time()),
                'version': '1.0',
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 保存为pkl格式
            with open(pkl_file, 'wb') as f:
                pickle.dump(pkl_data, f)

            # 删除旧的JSON缓存
            os.remove(json_file)

            # 加载到向量存储
            metadata = pkl_data.get('metadata', [{}] * len(pkl_data['examples']))
            self.vector_store.add_examples(pkl_data['examples'], pkl_data['embeddings'], metadata)
            logger.info(f"成功迁移并加载 {len(pkl_data['examples'])} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"迁移JSON缓存失败: {e}")
            return False

    def _validate_cache(self, cached_data: Dict[str, Any], data_path: str) -> bool:
        """验证缓存有效性"""
        try:
            # 检查必需字段
            required_fields = ['examples', 'embeddings', 'dataset_path']
            if not all(field in cached_data for field in required_fields):
                return False

            # 检查数据集是否已更改
            if cached_data['dataset_path'] != data_path:
                return False

            # 检查数据集文件哈希（如果存在）
            if 'dataset_hash' in cached_data:
                current_hash = self._get_dataset_hash(data_path)
                if cached_data['dataset_hash'] != current_hash:
                    logger.info("数据集文件已更改，缓存失效")
                    return False

            return True

        except Exception:
            return False

    def _get_dataset_hash(self, data_path: str) -> str:
        """获取数据集文件哈希"""
        try:
            with open(data_path, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    async def _generate_embeddings_in_batches(self, texts: List[str], batch_size: int = 100, max_concurrent: int = 10) -> List[List[float]]:
        """🚀 并发批量生成嵌入向量，大幅提升效率"""
        total_batches = (len(texts) + batch_size - 1) // batch_size

        logger.info(f"🚀 开始并发生成嵌入向量: {len(texts)} 个文本, {total_batches} 个批次, 最大并发: {max_concurrent} (优化版)")

        # 创建批次任务
        batch_tasks = []
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            batch_tasks.append(self._process_single_batch(batch_texts, batch_num, total_batches))

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(task):
            async with semaphore:
                return await task

        # 并发执行所有批次
        try:
            batch_results = await asyncio.gather(*[process_with_semaphore(task) for task in batch_tasks])

            # 合并结果
            all_embeddings = []
            for batch_embeddings in batch_results:
                if batch_embeddings:
                    all_embeddings.extend(batch_embeddings)
                else:
                    logger.error("某个批次嵌入生成失败")
                    return []

            logger.info(f"✅ 并发嵌入生成完成: {len(all_embeddings)} 个向量")
            return all_embeddings

        except Exception as e:
            logger.error(f"❌ 并发嵌入生成失败: {e}")
            return []

    async def _process_single_batch(self, batch_texts: List[str], batch_num: int, total_batches: int) -> List[List[float]]:
        """处理单个批次的嵌入生成"""
        try:
            logger.info(f"🔄 处理批次 {batch_num}/{total_batches}: {len(batch_texts)} 个文本")
            batch_embeddings = await self.model_service.get_embeddings_async(batch_texts)
            if batch_embeddings:
                logger.info(f"✅ 批次 {batch_num} 完成")
                return batch_embeddings
            else:
                logger.error(f"❌ 批次 {batch_num} 嵌入生成失败")
                return []
        except Exception as e:
            logger.error(f"❌ 批次 {batch_num} 处理失败: {e}")
            return []

    async def _generate_and_cache_vectors(self, data_path: str, pkl_cache_file: str) -> bool:
        """生成向量并保存到pkl缓存"""
        try:
            # 加载数据集
            with open(data_path, 'r', encoding='utf-8') as f:
                examples = json.load(f)

            # 批量生成语义向量（避免API超时）
            texts = [example.get('text', '') for example in examples]
            embeddings = await self._generate_embeddings_in_batches(texts, batch_size=50)

            if not embeddings or len(embeddings) != len(examples):
                logger.error(f"嵌入生成失败: 预期 {len(examples)}, 实际 {len(embeddings) if embeddings else 0}")
                return False

            # 添加到向量存储
            metadata = [{}] * len(examples)  # 简单metadata
            self.vector_store.add_examples(examples, embeddings, metadata)

            # 保存到pkl缓存
            cache_data = {
                'examples': examples,
                'embeddings': embeddings,
                'dataset_path': data_path,
                'created_at': time.time(),
                'version': '1.0',
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            with open(pkl_cache_file, 'wb') as f:
                pickle.dump(cache_data, f)

            logger.info(f"向量存储初始化完成，生成并缓存 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"生成向量缓存失败: {e}")
            return False

    async def initialize_vector_store(self, data_path: Optional[str] = None) -> bool:
        """🔍 KISS原则：检测有无向量库，有则继续，没有就生成"""
        try:
            # 获取数据集路径
            if data_path is None:
                data_path = get_current_dataset_path()

            if not os.path.exists(data_path):
                logger.warning(f"数据集文件不存在: {data_path}")
                return False

            # 简单的缓存检测
            cache_dir = CONFIG.get('vector_cache_dir', './cache/vector')
            os.makedirs(cache_dir, exist_ok=True)

            # 🔍 修复：包含数据集名称标识，避免不同数据集混淆
            from config import CONFIG as GLOBAL_CONFIG
            current_dataset_key = GLOBAL_CONFIG.get('current_dataset', 'unknown')
            file_name = os.path.basename(data_path).replace('.json', '')
            pkl_cache_file = os.path.join(cache_dir, f"{current_dataset_key}_{file_name}_vectors.pkl")

            # 检测向量库是否存在
            if os.path.exists(pkl_cache_file):
                logger.info("🔍 检测到现有向量库，正在加载...")
                if await self._load_pkl_cache(pkl_cache_file, data_path):
                    logger.info("✅ 向量库加载成功")
                    return True
                else:
                    logger.warning("⚠️ 向量库加载失败，重新生成...")

            # 向量库不存在或损坏，生成新的
            logger.info("🚀 向量库不存在，开始生成...")
            success = await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            if success:
                logger.info("✅ 向量库生成完成")
            return success

        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False




    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询嵌入 - 使用批处理器优化"""
        try:
            # 使用批处理器生成嵌入
            request_id = f"query_{hash(query)}_{time.time()}"
            embedding = await self.batch_processor.add_request(request_id, query)
            return embedding if embedding else []
        except Exception as e:
            logger.error(f"生成查询嵌入失败: {e}")
            return []

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1_array = np.array(vec1)
            vec2_array = np.array(vec2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1_array, vec2_array)
            norm1 = np.linalg.norm(vec1_array)
            norm2 = np.linalg.norm(vec2_array)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
                
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0

    async def reranker_refinement(self, query: str, candidates: List[Dict[str, Any]], top_k: int = 5) -> List[Dict[str, Any]]:
        """重排器优化：使用SiliconFlow重排器API优化相关性"""
        try:
            if not candidates:
                logger.warning("没有候选示例进行重排")
                return []
            
            if len(candidates) <= top_k:
                logger.info(f"候选数量 ({len(candidates)}) 不大于 top_k ({top_k})，跳过重排")
                return candidates
            
            # 准备重排器输入
            doc_texts = []
            for candidate in candidates:
                doc_text = f"Text: {candidate.get('text', '')}\nEntities: {candidate.get('label', {})}"
                doc_texts.append(doc_text)
            
            # 调用重排器API
            rerank_result = await self.model_service.rerank_async(
                query=query,
                documents=doc_texts,
                top_k=top_k
            )
            
            if not rerank_result:
                logger.warning("重排器未返回结果，返回原始顺序")
                return candidates[:top_k]
            
            # 根据重排结果重新排序
            reranked_candidates = []
            for i, rerank_item in enumerate(rerank_result):
                # 重排器返回结果按相关性排序，需要从原始candidates中获取对应项
                # 如果rerank_item包含index，使用它；否则使用位置索引
                index = rerank_item.get('index', i)
                if index < len(candidates):
                    candidate = candidates[index].copy()
                    candidate['rerank_score'] = rerank_item.get('score', 0.5)
                    reranked_candidates.append(candidate)
                else:
                    # 如果索引超出范围，使用位置索引
                    if i < len(candidates):
                        candidate = candidates[i].copy()
                        candidate['rerank_score'] = rerank_item.get('score', 0.5)
                        reranked_candidates.append(candidate)
            
            logger.info(f"重排器优化完成: 返回最相关的 {len(reranked_candidates)} 个示例")
            return reranked_candidates
            
        except Exception as e:
            logger.error(f"重排器优化失败: {e}")
            return candidates[:top_k]  # 回退


    async def simple_retrieve(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        高性能检索方法 - 使用FAISS+批处理优化

        Args:
            description: LLM生成的检索描述
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        try:
            if not self.initialized:
                logger.warning("⚠️ 向量存储未初始化")
                return []

            logger.info(f"🔍 高性能检索: {description[:50]}..., k={k}")

            # 生成查询嵌入（使用批处理器）
            query_embedding = await self._generate_query_embedding(description)
            if not query_embedding:
                logger.warning("查询嵌入生成失败")
                return []

            # 使用FAISS进行高速检索
            if isinstance(self.vector_store, FAISSVectorStore) and self.vector_store.initialized:
                # FAISS高速检索
                search_results = self.vector_store.search(query_embedding, top_k=k)

                results = []
                for idx, score in search_results:
                    if idx < len(self.vector_store.examples):
                        example = self.vector_store.examples[idx]
                        results.append({
                            'text': example.get('text', ''),
                            'label': example.get('label', {}),
                            'similarity_score': float(score)
                        })

                logger.info(f"✅ FAISS检索完成: 返回 {len(results)} 个示例")
                return results
            else:
                logger.warning("⚠️ FAISS向量存储未初始化")
                return []

        except Exception as e:
            logger.error(f"检索失败: {e}")
            return []




# 全局单例
example_retriever = ExampleRetriever()
</file>

<file path="main.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统
专注于数据集处理和评估的简化版本
"""

import asyncio
import argparse
import logging
import json

import os
from typing import Dict, Any, Optional
from datetime import datetime

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets
from pipeline import run_meta_cognitive_ner_pipeline


def setup_logging(level: str = "WARNING"):
    """设置日志配置"""
    import sys
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        stream=sys.stderr  # 确保日志不干扰tqdm
    )


def print_progress_bar(stage_name: str, current: int, total: int, width: int = 50):
    """打印美观的进度条"""
    percentage = current / total if total > 0 else 0
    filled = int(width * percentage)
    bar = '█' * filled + '░' * (width - filled)
    print(f"\r{stage_name} [{bar}] {percentage:.1%}", end='', flush=True)
    if current == total:
        print()  # 换行


def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧠 APIICL - 元认知智能体NER系统")
    print("📚 数据集处理和评估模式")
    print("=" * 60)


async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 处理数据集并进行评估"""

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 阶段1：数据准备和向量库预初始化
    print("📚 阶段1：数据准备和向量库预初始化")
    try:
        with open(test_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]

    # 🔍 KISS原则：预先初始化向量库，避免重复初始化
    print("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        print("✅ 向量库预初始化成功")
    else:
        print("⚠️ 向量库预初始化失败，将使用直接模式")

    print_progress_bar("📚 数据准备阶段", 1, 1)
    
    # 阶段2：模型推理
    print("� 阶段2：模型推理")

    # �🛠️ 修复：使用预初始化向量库，避免重复初始化导致的并发爆炸
    # 现在可以安全使用更高并发数，因为向量库已预初始化
    max_concurrent = CONFIG.get('max_concurrent_requests', 200)  # 恢复较高并发数
    semaphore_limit = min(max_concurrent, len(test_data))
    semaphore = asyncio.Semaphore(semaphore_limit)

    async def process_single_sample_with_semaphore(sample, sample_id):
        """带信号量控制的单样本处理 - 使用预初始化的向量库"""
        async with semaphore:
            try:
                text = sample.get('text', '')
                # 🔍 使用预初始化的向量库，避免重复初始化
                predicted_labels = await run_meta_cognitive_ner_pipeline(text, pre_initialized_retriever=global_retriever)
                return sample_id, sample, predicted_labels, None
            except Exception as e:
                return sample_id, sample, {}, e

    # 创建所有任务
    all_tasks = [
        process_single_sample_with_semaphore(sample, i)
        for i, sample in enumerate(test_data)
    ]

    results = []
    correct_predictions = 0
    total_entities = 0
    predicted_entities = 0

    # 流水线处理：边执行边处理结果
    completed_count = 0
    for completed_task in asyncio.as_completed(all_tasks):
        _, sample, predicted_labels, error = await completed_task  # 修复：正确解包所有变量
        completed_count += 1

        text = sample.get('text', '')
        true_labels = sample.get('label', {})

        if error:
            predicted_labels = {}
        elif not isinstance(predicted_labels, dict):
            predicted_labels = {}

        # 计算指标
        sample_correct = 0
        sample_total = sum(len(entities) for entities in true_labels.values())
        sample_predicted = sum(len(entities) for entities in predicted_labels.values())

        for entity_type, true_entities in true_labels.items():
            predicted_entities_of_type = predicted_labels.get(entity_type, [])
            for entity in true_entities:
                if entity in predicted_entities_of_type:
                    sample_correct += 1

        correct_predictions += sample_correct
        total_entities += sample_total
        predicted_entities += sample_predicted

        results.append({
            'text': text,
            'true_labels': true_labels,
            'predicted_labels': predicted_labels,
            'correct': sample_correct,
            'total_true': sample_total,
            'total_predicted': sample_predicted
        })

        # 更新进度条
        print_progress_bar("🚀 模型推理阶段", completed_count, len(test_data))

    print()  # 换行

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 处理样本数: {len(test_data)}")
    print(f"🎯 真实实体总数: {total_entities}")
    print(f"🔍 预测实体总数: {predicted_entities}")
    print(f"✅ 正确预测数: {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'samples_count': len(test_data),
        'total_entities': total_entities,
        'predicted_entities': predicted_entities,
        'correct_predictions': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'detailed_results': results
    }
    
    # 保存到文件 - 使用results文件夹
    results_dir = "results"
    os.makedirs(results_dir, exist_ok=True)  # 确保results目录存在
    eval_file = os.path.join(results_dir, f"eval_results_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        with open(eval_file, 'w', encoding='utf-8') as f:
            json.dump(eval_results, f, ensure_ascii=False, indent=2)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 数据集处理和评估"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统')
    parser.add_argument('--dataset', '-d', type=str, default='ace2005', 
                       help='数据集名称 (默认: ace2005)')
    parser.add_argument('--max-samples', type=int, 
                       help='最大测试样本数 (默认: 处理全部)')
    parser.add_argument('--log-level', type=str, default='WARNING', 
                       help='日志级别 (DEBUG/INFO/WARNING/ERROR)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置（智能检测标签集）
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print()
    
    try:
        # 直接进行数据集处理和评估
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
</file>

<file path="meta_cognitive_agent.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import asyncio
import logging
import json
from typing import Dict, List, Any, Optional

from config import CONFIG, get_current_dataset_info
from model_interface import model_service
from schemas import RetrieveNERExamplesTool, ExtractionTool

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""
    
    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体

        Args:
            example_retriever: 预初始化的示例检索器
        """
        # {{ AURA-X: Modify - 自动初始化示例检索器. Approval: 寸止(ID:1738230400). }}
        if example_retriever is None:
            from example_retriever import example_retriever as default_retriever
            self.example_retriever = default_retriever
        else:
            self.example_retriever = example_retriever
        self.model_service = model_service
        self._initialization_started = False
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')

    def _build_stage1_prompt(self, text: str) -> str:
        """
        构建Stage 1的prompt - 让LLM分析文本并生成检索请求
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""You are an expert Named Entity Recognition system with access to example retrieval capabilities.

Your task: Analyze the input text and determine what kind of NER examples would help you extract entities accurately.

Entity types to extract: {entity_types_str}

Input text: "{text}"

Analyze this text and call the retrieve_ner_examples tool to get helpful examples.
- Describe what specific examples you need (be very detailed)
- Specify how many examples you want (k=1-5)

You MUST call the retrieve_ner_examples tool."""

    async def _ensure_initialized(self):
        """确保示例检索器已初始化"""
        if not self._initialization_started and self.example_retriever and not self.example_retriever.initialized:
            self._initialization_started = True
            logger.info("🔧 自动初始化示例检索器...")
            await self.example_retriever.initialize_vector_store()

    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        🚀 两阶段NER流程

        Stage 1: LLM接受input并做function call，用description和k调用检索器函数得到few-shot
        Stage 2: 用这些few-shot拼接成为prompt做NER
        """
        try:
            # {{ AURA-X: Fix - 实现正确的两阶段流程. Approval: 寸止(ID:1738230400). }}
            await self._ensure_initialized()

            logger.info(f"🧠 Stage 1: 分析文本并生成检索请求: '{text[:50]}...'")

            # Stage 1: LLM分析文本并生成检索请求
            stage1_prompt = self._build_stage1_prompt(text)
            tools = [RetrieveNERExamplesTool]  # 只提供检索工具
            messages = [{"role": "user", "content": stage1_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行检索获取few-shot
                few_shot_examples = await self._execute_retrieval_stage(response.tool_calls)

                if few_shot_examples:
                    logger.info(f"🧠 Stage 2: 基于{len(few_shot_examples)}个示例进行NER")
                    # Stage 2: 基于few-shot进行NER
                    return await self._execute_ner_stage(text, few_shot_examples)
                else:
                    logger.warning("❌ 未获取到few-shot示例")
                    return {}
            else:
                logger.warning("❌ LLM未调用检索工具")
                return {}

        except Exception as e:
            logger.error(f"两阶段NER失败: {e}")
            return {}
    


    async def _execute_retrieval_stage(self, tool_calls: List[Any]) -> List[Any]:
        """
        执行Stage 1的检索阶段
        """
        for tool_call in tool_calls:
            if not tool_call.function:
                continue

            function_name = tool_call.function.name

            if function_name == "RetrieveNERExamplesTool":
                try:
                    arguments = json.loads(tool_call.function.arguments)
                    description = arguments.get("description", "")
                    k = arguments.get("k", 3)

                    logger.info(f"🔍 检索请求: {description[:50]}..., k={k}")

                    # 执行检索
                    examples = await self._simple_retrieval(description, k)
                    logger.info(f"✅ 检索到 {len(examples)} 个示例")
                    return examples

                except json.JSONDecodeError as e:
                    logger.error(f"解析检索参数失败: {e}")

        return []

    async def _execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        执行Stage 2的NER阶段 - 基于few-shot示例进行NER
        """
        # 构建包含few-shot的prompt
        examples_text = self._format_examples_for_context(few_shot_examples)
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        ner_prompt = f"""You are an expert Named Entity Recognition system.

Here are some examples to help you:

{examples_text}

Now extract named entities from the following text using these entity types: {entity_types_str}

Text to analyze: "{text}"

Return the entities in JSON format like: {{"person": ["John"], "organization": ["Apple Inc."], "location": ["New York"]}}"""

        # 调用LLM进行NER（纯文本生成，不是Function Call）
        messages = [{"role": "user", "content": ner_prompt}]

        response = await self.model_service.generate_simple_async(
            messages=messages,
            temperature=0.1
        )

        if response:
            try:
                # 解析JSON结果
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    entities_json = json_match.group()
                    entities = json.loads(entities_json)
                    logger.info(f"✅ Stage 2完成，提取到 {sum(len(v) for v in entities.values())} 个实体")
                    return entities
                else:
                    logger.warning("❌ 无法从响应中提取JSON")
                    return {}
            except json.JSONDecodeError as e:
                logger.error(f"解析NER结果失败: {e}")
                return {}
        else:
            logger.warning("❌ Stage 2 NER失败")
            return {}

    async def _simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        🔍 简化的检索方法 - 直接基于description检索k个示例
        """
        try:
            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            # {{ AURA-X: Fix - 使用简单检索方法，直接接受Function Call参数. Approval: 寸止(ID:1738230400). }}
            # 直接使用description和k进行检索
            examples = await self.example_retriever.simple_retrieve(description, k)
            return examples

        except Exception as e:
            logger.error(f"简化检索失败: {e}")
            return []

    def _format_examples_for_context(self, examples) -> str:
        """将检索到的示例格式化为上下文"""
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            # {{ AURA-X: Fix - 处理ScoredExample对象. Approval: 寸止(ID:1738230400). }}
            # 检查是否是ScoredExample对象
            if hasattr(example, 'example'):
                # ScoredExample对象
                example_data = example.example
            else:
                # 普通字典
                example_data = example

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            entities_str = ", ".join(
                f"'{entity}' ({etype})"
                for etype, entities in labels.items()
                for entity in entities
            )

            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)


# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
</file>

<file path="config.py">
import os
import json
from typing import Dict, List, Set
from collections import Counter

CONFIG = {
    # ====== 🚀 核心配置 ======
    'dataset': 'ACE 2005',
    'max_test_samples': 1500,
    'embedding_model_path': 'BAAI/bge-m3',
    'reranker_model': 'BAAI/bge-reranker-v2-m3',

    # ====== 🔗 API配置 ======
    'base_url': 'http://8.138.94.162:3001/proxy/silicon/v1',
    'model_name': 'Qwen/Qwen2.5-7B-Instruct',
    'api_key': 'sk-zhongyushi',
    # {{ AURA-X: Modify - 优化API配置，提升高并发性能. Approval: 寸止(ID:1738230400). }}
    'max_concurrent_requests': 500,  # 用户API站并发量高，提升到500
    'timeout_seconds': 60,  # 降低超时，快速失败
    'api_timeout': 60,  # 降低API超时配置
    'max_retries': 3,  # 保持重试配置
    'retry_delay': 2,  # 降低重试延迟

    # ====== 📊 界面配置 ======
    'log_level': 'WARNING',
    'show_progress': True,
    'progress_desc': '🧠 Processing NER',

    # ====== 📁 路径配置 ======
    'vector_cache_dir': './cache/vector',
    'vector_db_path': './cache/chromadb',
    'data_root_dir': './data',

    # ====== 🧠 元认知配置 ======
    'meta_cognitive_config': {
        'difficulty_analysis_temperature': 0.1,
        'requirement_generation_temperature': 0.2,
        'structured_conversion_temperature': 0.0,
        'max_difficulties': 5,
        'max_requirements': 3,
    },

    # ====== 🔍 检索配置 ======
    'retrieval_config': {
        'vector_top_k': 30,
        'reranker_top_k': 10,
        'final_examples_count': 3,
        'score_threshold': 0.1,
        'diversity_lambda': 0.3,
        'fallback_examples_count': 2,
    },

    # ====== 🤖 LangChain配置 ======
    'langchain_config': {
        'enable_langchain': True,
        'enable_langsmith': False,
        'enable_fallback': True,
    },

    # ====== 🎯 提示词模板 ======
    'system_prompt_template': """You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below:
{label_set_info}

### Context and Examples ###
{examples_str}

### Text to Analyze ###
{user_query}

OUTPUT FORMAT: Return a JSON object where keys are entity types and values are arrays of entity strings.
- If entities are found: {output_format_example}
- If no entities are found: {{}}
- Only include entity types that have found entities
- Do not include empty arrays

OUTPUT: Valid JSON only. No explanations.""",

    # ====== 📊 数据集配置 ======
    'current_dataset': 'ace2005',
    'datasets': {
        'ace2005': {
            'name': 'ACE 2005',
            'path': 'data/ACE 2005/train.json',
            'labels': ['person', 'organization', 'location', 'facility', 'weapon', 'vehicle', 'geo-political'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from ACE 2005 dataset:**
- person: People, individuals, groups (e.g., "John Smith", "Mary Johnson", "the team")
- organization: Companies, institutions, agencies (e.g., "Apple Inc.", "Microsoft", "FBI")
- location: Places, addresses, geographic locations (e.g., "New York", "California", "Main Street")
- facility: Buildings, structures, installations (e.g., "White House", "airport", "hospital")
- weapon: Weapons, armaments (e.g., "rifle", "missile", "bomb")
- vehicle: Transportation vehicles (e.g., "car", "airplane", "ship")
- geo-political: Geographic and political entities (e.g., "United States", "European Union", "NATO")"""
        },
        'conll2003': {
            'name': 'CoNLL 2003',
            'path': 'data/CoNLL2003/train.json',
            'labels': ['PER', 'ORG', 'LOC', 'MISC'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from CoNLL 2003 dataset:**
- PER: Person names (e.g., "John Smith", "Mary Johnson")
- ORG: Organizations (e.g., "Apple Inc.", "Microsoft")
- LOC: Locations (e.g., "New York", "California")
- MISC: Miscellaneous named entities"""
        }
    }
}

# ====== 🛠️ 工具函数 ======
def set_dataset(dataset_key: str) -> bool:
    """切换数据集"""
    if dataset_key in CONFIG['datasets']:
        CONFIG['current_dataset'] = dataset_key
        return True
    return False

def get_current_dataset_info() -> dict:
    """获取当前数据集信息"""
    return CONFIG['datasets'][CONFIG['current_dataset']]

def get_current_dataset_path() -> str:
    """获取当前数据集路径"""
    return get_current_dataset_info()['path']

def get_current_label_prompt() -> str:
    """获取当前数据集的标签prompt"""
    return get_current_dataset_info()['label_prompt']

def list_available_datasets() -> dict:
    """列出所有可用数据集"""
    available = {}
    for key, info in CONFIG['datasets'].items():
        available[key] = {
            'name': info['name'],
            'available': os.path.exists(info['path']),
            'current': key == CONFIG['current_dataset'],
            'labels': info.get('labels', [])
        }
    return available


# ====== 🧠 智能标签集检测 ======

def detect_dataset_labels(dataset_path: str, sample_size: int = 100) -> Dict[str, int]:
    """
    智能检测数据集中的标签集

    Args:
        dataset_path: 数据集文件路径
        sample_size: 采样数量，用于分析

    Returns:
        Dict[str, int]: 标签及其出现频次
    """
    if not os.path.exists(dataset_path):
        return {}

    try:
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 限制采样数量以提高性能
        if len(data) > sample_size:
            data = data[:sample_size]

        label_counter = Counter()

        for item in data:
            labels = item.get('label', {})
            if isinstance(labels, dict):
                for label_type, entities in labels.items():
                    if isinstance(entities, list) and entities:
                        label_counter[label_type] += len(entities)

        return dict(label_counter)

    except Exception as e:
        print(f"⚠️ 检测标签集失败 {dataset_path}: {e}")
        return {}


def auto_detect_and_update_labels():
    """
    自动检测数据集中的实际标签并更新配置
    使用数据驱动的方式，以实际数据为准
    """
    print("🔍 开始智能检测数据集标签集...")

    for dataset_key, dataset_info in CONFIG['datasets'].items():
        dataset_path = dataset_info['path']
        print(f"📊 检测数据集: {dataset_info['name']}")

        detected_labels = detect_dataset_labels(dataset_path)

        if detected_labels:
            # 按频次排序，取最常见的标签
            sorted_labels = sorted(detected_labels.items(), key=lambda x: x[1], reverse=True)
            detected_label_names = [label for label, _ in sorted_labels]

            # 直接使用检测到的标签更新配置
            CONFIG['datasets'][dataset_key]['labels'] = detected_label_names
            CONFIG['datasets'][dataset_key]['detected_labels'] = detected_labels

            print(f"  ✅ 检测到 {len(detected_label_names)} 个标签: {detected_label_names}")

            # 根据检测到的标签重新生成label_prompt
            label_descriptions = generate_label_descriptions(detected_label_names)
            CONFIG['datasets'][dataset_key]['label_prompt'] = label_descriptions
        else:
            print("  ❌ 未检测到标签")

    print("🎉 标签集检测完成！")


def generate_label_descriptions(labels: List[str]) -> str:
    """
    根据标签名称生成描述性提示

    Args:
        labels: 标签列表

    Returns:
        str: 格式化的标签描述
    """
    # 标签描述映射
    label_desc_map = {
        # CoNLL 2003 格式
        'PER': 'Person names',
        'ORG': 'Organizations',
        'LOC': 'Locations',
        'MISC': 'Miscellaneous entities',

        # ACE 2005 格式
        'person': 'Person names',
        'organization': 'Organizations',
        'location': 'Locations',
        'geographical-social-political': 'Geographic and political entities',
        'weapon': 'Weapons',
        'facility': 'Facilities',
        'vehicle': 'Vehicles',

        # 通用格式
        'date': 'Dates and times',
        'money': 'Monetary values',
        'percent': 'Percentages',
        'time': 'Time expressions',
        'cardinal': 'Cardinal numbers',
        'ordinal': 'Ordinal numbers'
    }

    descriptions = []
    for label in labels:
        desc = label_desc_map.get(label, f"{label.title()} entities")
        descriptions.append(f"- {label}: {desc}")

    return "Entity types:\n" + "\n".join(descriptions)


def get_current_labels() -> List[str]:
    """获取当前数据集的标签列表"""
    current_dataset = get_current_dataset_info()
    return current_dataset.get('labels', [])


def get_optimized_label_prompt() -> str:
    """获取优化后的标签提示"""
    labels = get_current_labels()

    if not labels:
        return "Entity types: No specific entity types defined."

    return generate_label_descriptions(labels)


def get_current_output_format_example() -> str:
    """根据当前数据集生成正确的输出格式示例"""
    current_dataset = get_current_dataset_info()
    labels = current_dataset.get('labels', [])

    if not labels:
        return '{}'

    # 为每个标签生成示例
    examples = {}
    for label in labels[:3]:  # 只取前3个标签作为示例
        if label.lower() in ['person', 'per']:
            examples[label] = ["John", "Mary"]
        elif label.lower() in ['organization', 'org']:
            examples[label] = ["Apple Inc."]
        elif label.lower() in ['location', 'loc']:
            examples[label] = ["New York"]
        else:
            # 为其他标签生成通用示例
            examples[label] = ["example"]

    import json
    return json.dumps(examples)


def initialize_datasets():
    """
    初始化数据集配置，自动检测标签集
    在程序启动时调用一次
    """
    print("🚀 初始化数据集配置...")

    # 检查是否需要更新标签集
    need_update = False
    for dataset_key, dataset_info in CONFIG['datasets'].items():
        if 'detected_labels' not in dataset_info:
            need_update = True
            break

    if need_update:
        auto_detect_and_update_labels()
    else:
        print("✅ 数据集标签集已是最新状态")
</file>

<file path="model_interface.py">
import logging
import asyncio
from typing import List, Dict, Any, Optional, Type
from pydantic import BaseModel

from openai import AsyncOpenAI
import aiohttp
from config import CONFIG

# 配置日志
logger = logging.getLogger(__name__)

def pydantic_to_openai_tool(pydantic_model: Type[BaseModel]) -> Dict[str, Any]:
    """将Pydantic模型转换为OpenAI Function Calling工具的JSON Schema。"""
    # Pydantic v2 has .model_json_schema()
    schema = pydantic_model.model_json_schema()
    return {
        "type": "function",
        "function": {
            "name": schema.get('title', pydantic_model.__name__),
            "description": schema.get('description', ''),
            "parameters": schema
        }
    }

class ModelService:
    """
    统一的模型服务层 - 极简版
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ModelService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.config = CONFIG
            self.base_url = self.config.get('base_url')
            self.model_name = self.config.get('model_name')
            self.api_key = self.config.get('api_key')
            self.timeout = self.config.get('api_timeout', 60)  # {{ AURA-X: Modify - 降低超时时间. Approval: 寸止(ID:1738230400). }}

            self.max_concurrent_requests = self.config.get('max_concurrent_requests', 500)  # {{ AURA-X: Modify - 提升并发数. Approval: 寸止(ID:1738230400). }}
            self.semaphore = asyncio.Semaphore(self.max_concurrent_requests)

            self.api_max_retries = self.config.get('max_retries', 3)
            self.api_retry_delay = self.config.get('retry_delay', 2)  # {{ AURA-X: Modify - 降低重试延迟. Approval: 寸止(ID:1738230400). }}

            # {{ AURA-X: Add - HTTP连接池优化，复用连接. Approval: 寸止(ID:1738230400). }}
            self._client_pool = {}  # 连接池，复用AsyncOpenAI客户端

            self.initialized = True
            logger.info(f"ModelService (优化版) initialized: concurrent_requests={self.max_concurrent_requests}, timeout={self.timeout}s")

    async def _get_client(self) -> AsyncOpenAI:
        # {{ AURA-X: Modify - 实现连接池复用，提升性能. Approval: 寸止(ID:1738230400). }}
        client_key = f"{self.base_url}_{(self.api_key or '')[:10]}"
        if client_key not in self._client_pool:
            self._client_pool[client_key] = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout,
            )
        return self._client_pool[client_key]

    async def generate_with_tools_async(self, messages: List[Dict], tools: List[Type[BaseModel]]):
        """
        异步调用LLM，并使用Function Calling。
        """
        if not self.api_key:
            logger.error("No API key configured.")
            return None

        tool_schemas = [pydantic_to_openai_tool(tool) for tool in tools]

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.chat.completions.create(
                        model=self.model_name,
                        messages=messages,
                        tools=tool_schemas,
                        tool_choice="auto",  # {{ AURA-X: Fix - 让LLM自主选择调用工具. Approval: 寸止(ID:1738230400). }}
                    )
                    # {{ AURA-X: Remove - 不关闭连接，复用连接池. Approval: 寸止(ID:1738230400). }}
                    # await client.close()  # 保持连接复用
                    return response.choices[0].message

                except Exception as e:
                    logger.warning(f"API call failed on attempt {attempt + 1}: {e}")
                    if "context canceled" in str(e).lower():
                        logger.error(f"❌ Context canceled detected on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("API call failed after multiple retries.")
                        return None
    
    async def get_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """🔍 异步获取文本嵌入向量"""
        if not texts or not self.api_key:
            return []

        model_to_use = self.config['embedding_model_path']

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.embeddings.create(model=model_to_use, input=texts)
                    await client.close()
                    logger.debug(f"✅ Embeddings generated for {len(texts)} texts")
                    return [item.embedding for item in response.data]

                except Exception as e:
                    logger.warning(f"⚠️ Embedding call failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Embedding call failed after multiple retries.")
                        return []
        return []

    async def rerank_async(self, query: str, documents: List[str], top_k: int = 10) -> List[Dict[str, Any]]:
        """🎯 SiliconFlow重排器API - 核心需求驱动检索组件"""
        if not documents or not self.api_key:
            logger.warning("⚠️ No documents or API key for reranking")
            return []

        # 使用SiliconFlow的重排器模型
        reranker_model = self.config.get('reranker_model', 'BAAI/bge-reranker-v2-m3')

        # 使用aiohttp进行重排器API调用
        import aiohttp

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    # 构建重排器请求URL - 基于SiliconFlow API文档
                    rerank_url = f"{self.base_url}/rerank"  # 🚀 修复：简化URL构建

                    payload = {
                        "model": reranker_model,
                        "query": query,
                        "documents": documents
                    }

                    headers = {
                        "Authorization": f"Bearer {self.api_key}",
                        "Content-Type": "application/json"
                    }

                    async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=self.timeout)) as session:
                        async with session.post(rerank_url, json=payload, headers=headers) as response:
                            if response.status == 200:
                                result = await response.json()
                                results = result.get('results', [])
                                # 限制返回数量
                                limited_results = results[:top_k] if top_k else results
                                logger.info(f"🎯 Reranker processed {len(documents)} docs, returned top-{len(limited_results)}")
                                return limited_results
                            else:
                                error_text = await response.text()
                                logger.error(f"❌ Reranker API error: {response.status} - {error_text}")
                                return []

                except Exception as e:
                    logger.warning(f"⚠️ Reranker call failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Reranker call failed after multiple retries.")
                        return []
        return []

    async def generate_simple_async(self, messages: List[Dict], temperature: float = 0.1) -> str:
        """🧠 简单文本生成 - 用于元认知规划器的三阶段Prompt链"""
        if not self.api_key:
            logger.error("❌ No API key configured for text generation")
            return ""

        async with self.semaphore:
            for attempt in range(self.api_max_retries):
                try:
                    client = await self._get_client()
                    response = await client.chat.completions.create(
                        model=self.model_name,
                        messages=messages,
                        temperature=temperature,
                        max_tokens=1000  # 适合规划阶段的输出长度
                    )
                    # {{ AURA-X: Remove - 不关闭连接，复用连接池. Approval: 寸止(ID:1738230400). }}
                    # await client.close()  # 保持连接复用

                    content = response.choices[0].message.content or ""
                    logger.debug(f"✅ Generated {len(content)} characters of text")
                    return content

                except Exception as e:
                    logger.warning(f"⚠️ Simple generation failed on attempt {attempt + 1}: {e}")
                    if attempt < self.api_max_retries - 1:
                        await asyncio.sleep(self.api_retry_delay)
                    else:
                        logger.error("❌ Simple generation failed after multiple retries.")
                        return ""
        return ""

    # ====== 🚀 真正的并发API调用 ======

    async def batch_generate_async(self, requests: List[Dict[str, Any]]) -> List[Any]:
        """
        🚀 批量并发API调用 - 真正的并发，不等待单个响应

        Args:
            requests: 请求列表，每个请求包含 {'type': 'chat'/'embedding'/'rerank', 'params': {...}}

        Returns:
            List[Any]: 对应的响应结果列表
        """
        if not requests:
            return []

        logger.info(f"🚀 Starting batch concurrent requests: {len(requests)} requests")

        # 创建所有任务，不等待
        tasks = []
        for i, request in enumerate(requests):
            request_type = request.get('type')
            params = request.get('params', {})

            if request_type == 'chat':
                task = self._single_chat_request(params, request_id=i)
            elif request_type == 'embedding':
                task = self._single_embedding_request(params, request_id=i)
            elif request_type == 'rerank':
                task = self._single_rerank_request(params, request_id=i)
            else:
                logger.warning(f"⚠️ Unknown request type: {request_type}")
                task = asyncio.create_task(self._dummy_request())

            tasks.append(task)

        # 批量发送，等待所有结果
        logger.info(f"🚀 Sending {len(tasks)} concurrent requests...")
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Request {i} failed: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r is not None)
        logger.info(f"🎉 Batch requests completed: {success_count}/{len(requests)} successful")

        return processed_results

    async def _single_chat_request(self, params: Dict[str, Any], request_id: int = 0) -> Any:
        """单个聊天请求"""
        messages = params.get('messages', [])
        tools = params.get('tools', [])
        temperature = params.get('temperature', 0.1)

        if tools:
            return await self.generate_with_tools_async(messages, tools)
        else:
            return await self.generate_simple_async(messages, temperature)

    async def _single_embedding_request(self, params: Dict[str, Any], request_id: int = 0) -> List[List[float]]:
        """单个嵌入请求"""
        texts = params.get('texts', [])
        return await self.get_embeddings_async(texts)

    async def _single_rerank_request(self, params: Dict[str, Any], request_id: int = 0) -> List[Dict[str, Any]]:
        """单个重排请求"""
        query = params.get('query', '')
        documents = params.get('documents', [])
        top_k = params.get('top_k', 10)
        return await self.rerank_async(query, documents, top_k)

    async def _dummy_request(self) -> None:
        """虚拟请求，用于处理未知类型"""
        await asyncio.sleep(0.1)
        return None


# 全局单例
model_service = ModelService()
</file>

<file path="pipeline.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 Pipeline模块 - 元认知智能体架构
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import logging
from typing import Dict, List

# 🧠 新架构：使用元认知智能体
from meta_cognitive_agent import get_meta_cognitive_agent

logger = logging.getLogger(__name__)

# --- 🧠 元认知智能体NER流程 ---

async def run_meta_cognitive_ner_pipeline(query: str, pre_initialized_retriever=None) -> Dict[str, List[str]]:
    """
    🧠 元认知智能体NER流水线

    核心创新：
    1. 单一超级Prompt：LLM一次性完成思考、决策和执行
    2. Function Calling驱动：LLM自主决策是否需要检索示例
    3. 多路并行检索：语义优先 + 标签辅助 + 智能融合
    4. 三级火箭检索：硬性过滤 → 语义召回 → 重排器精排
    """
    try:
        logger.info(f"🧠 启动元认知智能体NER: '{query[:50]}...'")

        # 🎯 核心：使用元认知智能体
        agent = get_meta_cognitive_agent(pre_initialized_retriever)

        # 🚀 一次调用完成所有任务
        entities = await agent.extract_entities(query)

        # 显示结果
        if entities:
            total_entities = sum(len(v) for v in entities.values() if isinstance(v, list))

            if total_entities > 0:
                logger.info(f"✅ 元认知智能体NER完成: 识别 {total_entities} 个实体")

                print("\n" + "="*60)
                print("🧠 元认知智能体NER结果")
                print("="*60)
                print(f"✅ 成功识别 {total_entities} 个实体")
                for entity_type, entity_list in entities.items():
                    if entity_list:  # 只显示非空的实体类型
                        print(f"🏷️ {entity_type}: {entity_list}")
                print("="*60)
            else:
                logger.warning("⚠️ 未识别到任何实体")
        else:
            logger.warning("⚠️ 未识别到任何实体")

        # 🔧 清理结果：移除空数组，只返回有实体的类型
        if entities:
            cleaned_entities = {k: v for k, v in entities.items() if isinstance(v, list) and v}
            return cleaned_entities
        else:
            return {}

    except Exception as e:
        logger.error(f"❌ 元认知智能体NER失败: {e}")
        return {}


# 🎯 重构完成 - 真正的元认知智能体架构
# 使用方式：
# from pipeline import run_meta_cognitive_ner_pipeline
# result = await run_meta_cognitive_ner_pipeline("your query here", pre_initialized_retriever)
</file>

</files>
